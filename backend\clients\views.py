from rest_framework import generics
from .models import Client, Testimonial
from .serializers import ClientSerializer, TestimonialSerializer


class ClientListView(generics.ListAPIView):
    queryset = Client.objects.filter(active=True)
    serializer_class = ClientSerializer
    ordering = ['-featured', 'order', 'name']


class FeaturedClientsView(generics.ListAPIView):
    queryset = Client.objects.filter(active=True, featured=True)
    serializer_class = ClientSerializer
    ordering = ['order', 'name']


class TestimonialListView(generics.ListAPIView):
    queryset = Testimonial.objects.filter(active=True)
    serializer_class = TestimonialSerializer
    ordering = ['-featured', 'order', '-created_at']


class FeaturedTestimonialsView(generics.ListAPIView):
    queryset = Testimonial.objects.filter(active=True, featured=True)
    serializer_class = TestimonialSerializer
    ordering = ['order', '-created_at']
