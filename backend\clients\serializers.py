from rest_framework import serializers
from .models import Client, Testimonial


class ClientSerializer(serializers.ModelSerializer):
    class Meta:
        model = Client
        fields = [
            'id', 'name', 'logo', 'logo_dark', 'website_url', 
            'description', 'featured', 'order'
        ]


class TestimonialSerializer(serializers.ModelSerializer):
    client = ClientSerializer(read_only=True)
    
    class Meta:
        model = Testimonial
        fields = [
            'id', 'client', 'author_name', 'author_position', 
            'author_image', 'content', 'rating', 'featured', 'created_at'
        ]
