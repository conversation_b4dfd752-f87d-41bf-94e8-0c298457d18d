from django.db import models


class ContactSubmission(models.Model):
    SERVICE_CHOICES = [
        ('web_development', 'Web Development & UX/UI Design'),
        ('digital_marketing', 'Digital Marketing'),
        ('seo', 'SEO (Search Engine Optimization)'),
        ('sem', 'SEM (Search Engine Marketing)'),
        ('graphic_design', 'Graphic Design'),
        ('consultation', 'Consultation'),
        ('other', 'Other'),
    ]
    
    BUDGET_CHOICES = [
        ('under_5k', 'Under R5,000'),
        ('5k_15k', 'R5,000 - R15,000'),
        ('15k_50k', 'R15,000 - R50,000'),
        ('50k_100k', 'R50,000 - R100,000'),
        ('over_100k', 'Over R100,000'),
        ('not_sure', 'Not Sure'),
    ]
    
    STATUS_CHOICES = [
        ('new', 'New'),
        ('contacted', 'Contacted'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('archived', 'Archived'),
    ]
    
    # Contact Information
    name = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=20, blank=True)
    company = models.CharField(max_length=100, blank=True)
    
    # Project Information
    service = models.CharField(max_length=50, choices=SERVICE_CHOICES)
    budget = models.CharField(max_length=20, choices=BUDGET_CHOICES, blank=True)
    timeline = models.CharField(max_length=100, blank=True)
    
    # Message
    message = models.TextField()
    
    # Additional Information
    how_did_you_hear = models.CharField(max_length=200, blank=True)
    website_url = models.URLField(blank=True, null=True)
    
    # Internal tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='new')
    notes = models.TextField(blank=True)
    assigned_to = models.CharField(max_length=100, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Contact Submission'
        verbose_name_plural = 'Contact Submissions'
    
    def __str__(self):
        return f"{self.name} - {self.service} ({self.created_at.strftime('%Y-%m-%d')})"


class Newsletter(models.Model):
    email = models.EmailField(unique=True)
    name = models.CharField(max_length=100, blank=True)
    active = models.BooleanField(default=True)
    
    # Timestamps
    subscribed_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-subscribed_at']
    
    def __str__(self):
        return f"{self.email} ({'Active' if self.active else 'Inactive'})"
