from rest_framework import generics, filters
from rest_framework.decorators import api_view
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from .models import Category, Technology, Project
from .serializers import (
    CategorySerializer, TechnologySerializer, 
    ProjectListSerializer, ProjectDetailSerializer
)


class CategoryListView(generics.ListAPIView):
    queryset = Category.objects.all()
    serializer_class = CategorySerializer


class TechnologyListView(generics.ListAPIView):
    queryset = Technology.objects.all()
    serializer_class = TechnologySerializer


class ProjectListView(generics.ListAPIView):
    queryset = Project.objects.filter(status='published')
    serializer_class = ProjectListSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchBackend, filters.OrderingFilter]
    filterset_fields = ['category', 'technologies', 'featured']
    search_fields = ['title', 'description', 'client_name']
    ordering_fields = ['created_at', 'title']
    ordering = ['-featured', 'order', '-created_at']


class ProjectDetailView(generics.RetrieveAPIView):
    queryset = Project.objects.filter(status='published')
    serializer_class = ProjectDetailSerializer
    lookup_field = 'slug'


class FeaturedProjectsView(generics.ListAPIView):
    queryset = Project.objects.filter(status='published', featured=True)
    serializer_class = ProjectListSerializer
    ordering = ['order', '-created_at']


@api_view(['GET'])
def portfolio_stats(request):
    """Return portfolio statistics"""
    stats = {
        'total_projects': Project.objects.filter(status='published').count(),
        'featured_projects': Project.objects.filter(status='published', featured=True).count(),
        'categories': Category.objects.count(),
        'technologies': Technology.objects.count(),
    }
    return Response(stats)
