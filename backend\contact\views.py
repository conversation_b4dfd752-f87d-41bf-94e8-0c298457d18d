from rest_framework import generics, status
from rest_framework.decorators import api_view
from rest_framework.response import Response
from django.core.mail import send_mail
from django.conf import settings
from .models import ContactSubmission, Newsletter
from .serializers import ContactSubmissionSerializer, NewsletterSerializer


class ContactSubmissionCreateView(generics.CreateAPIView):
    queryset = ContactSubmission.objects.all()
    serializer_class = ContactSubmissionSerializer
    
    def perform_create(self, serializer):
        submission = serializer.save()
        
        # Send notification email to admin
        try:
            self.send_admin_notification(submission)
        except Exception as e:
            # Log the error but don't fail the submission
            print(f"Failed to send admin notification: {e}")
        
        # Send confirmation email to user
        try:
            self.send_user_confirmation(submission)
        except Exception as e:
            print(f"Failed to send user confirmation: {e}")
    
    def send_admin_notification(self, submission):
        """Send notification email to admin"""
        subject = f"New Contact Form Submission - {submission.service}"
        message = f"""
        New contact form submission received:
        
        Name: {submission.name}
        Email: {submission.email}
        Phone: {submission.phone}
        Company: {submission.company}
        Service: {submission.get_service_display()}
        Budget: {submission.get_budget_display() if submission.budget else 'Not specified'}
        Timeline: {submission.timeline}
        
        Message:
        {submission.message}
        
        How they heard about us: {submission.how_did_you_hear}
        Website: {submission.website_url}
        
        Submitted at: {submission.created_at}
        """
        
        send_mail(
            subject,
            message,
            settings.DEFAULT_FROM_EMAIL,
            ['<EMAIL>'],  # Replace with actual admin email
            fail_silently=False,
        )
    
    def send_user_confirmation(self, submission):
        """Send confirmation email to user"""
        subject = "Thank you for contacting Cooper Systems"
        message = f"""
        Hi {submission.name},
        
        Thank you for reaching out to Cooper Systems! We've received your inquiry about {submission.get_service_display()}.
        
        Our team will review your request and get back to you within 24 hours.
        
        In the meantime, feel free to check out our portfolio at www.coopersystems.co.za
        
        Best regards,
        The Cooper Systems Team
        
        ---
        Digital Ingenuity, Designed in Midrand
        """
        
        send_mail(
            subject,
            message,
            settings.DEFAULT_FROM_EMAIL,
            [submission.email],
            fail_silently=False,
        )


class NewsletterSubscribeView(generics.CreateAPIView):
    queryset = Newsletter.objects.all()
    serializer_class = NewsletterSerializer
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        
        return Response(
            {"message": "Successfully subscribed to newsletter!"},
            status=status.HTTP_201_CREATED
        )


@api_view(['GET'])
def contact_info(request):
    """Return contact information"""
    info = {
        'company': 'Cooper Systems',
        'tagline': 'Digital Ingenuity, Designed in Midrand',
        'location': 'Midrand, South Africa',
        'email': '<EMAIL>',
        'phone': '+27 11 123 4567',
        'whatsapp': '+27 82 123 4567',
        'address': {
            'street': '123 Business Street',
            'city': 'Midrand',
            'province': 'Gauteng',
            'postal_code': '1685',
            'country': 'South Africa'
        },
        'social_media': {
            'linkedin': 'https://linkedin.com/company/cooper-systems',
            'twitter': 'https://twitter.com/coopersystems',
            'facebook': 'https://facebook.com/coopersystems',
            'instagram': 'https://instagram.com/coopersystems'
        },
        'business_hours': {
            'monday_friday': '08:00 - 17:00',
            'saturday': '09:00 - 13:00',
            'sunday': 'Closed'
        }
    }
    return Response(info)
