from django.contrib import admin
from .models import Category, Technology, Project, ProjectImage


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'slug', 'created_at']
    prepopulated_fields = {'slug': ('name',)}
    search_fields = ['name']


@admin.register(Technology)
class TechnologyAdmin(admin.ModelAdmin):
    list_display = ['name', 'color']
    search_fields = ['name']


class ProjectImageInline(admin.TabularInline):
    model = ProjectImage
    extra = 1
    fields = ['image', 'caption', 'order']


@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ['title', 'client_name', 'category', 'status', 'featured', 'created_at']
    list_filter = ['status', 'featured', 'category', 'technologies']
    search_fields = ['title', 'client_name', 'description']
    prepopulated_fields = {'slug': ('title',)}
    filter_horizontal = ['technologies']
    inlines = [ProjectImageInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'slug', 'description', 'short_description')
        }),
        ('Project Details', {
            'fields': ('client_name', 'project_url', 'github_url', 'featured_image')
        }),
        ('Categorization', {
            'fields': ('category', 'technologies')
        }),
        ('Status & Display', {
            'fields': ('status', 'featured', 'order')
        }),
    )
