from django.contrib import admin
from .models import Client, Testimonial


class TestimonialInline(admin.TabularInline):
    model = Testimonial
    extra = 0
    fields = ['author_name', 'author_position', 'content', 'rating', 'featured', 'active']


@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = ['name', 'featured', 'active', 'order', 'created_at']
    list_filter = ['featured', 'active']
    search_fields = ['name', 'description']
    list_editable = ['featured', 'active', 'order']
    inlines = [TestimonialInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'website_url')
        }),
        ('Logos', {
            'fields': ('logo', 'logo_dark')
        }),
        ('Display Settings', {
            'fields': ('featured', 'active', 'order')
        }),
    )


@admin.register(Testimonial)
class TestimonialAdmin(admin.ModelAdmin):
    list_display = ['author_name', 'client', 'rating', 'featured', 'active', 'created_at']
    list_filter = ['rating', 'featured', 'active', 'client']
    search_fields = ['author_name', 'content', 'client__name']
    list_editable = ['featured', 'active']
    
    fieldsets = (
        ('Author Information', {
            'fields': ('client', 'author_name', 'author_position', 'author_image')
        }),
        ('Testimonial Content', {
            'fields': ('content', 'rating')
        }),
        ('Display Settings', {
            'fields': ('featured', 'active', 'order')
        }),
    )
