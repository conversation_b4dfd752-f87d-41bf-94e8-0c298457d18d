from django.db import models


class Client(models.Model):
    name = models.Char<PERSON>ield(max_length=200)
    logo = models.ImageField(upload_to='clients/logos/')
    logo_dark = models.ImageField(upload_to='clients/logos/', blank=True, null=True)
    website_url = models.URLField(blank=True, null=True)
    description = models.TextField(blank=True)
    
    # Display settings
    featured = models.BooleanField(default=False)
    order = models.PositiveIntegerField(default=0)
    active = models.BooleanField(default=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-featured', 'order', 'name']
    
    def __str__(self):
        return self.name


class Testimonial(models.Model):
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='testimonials')
    author_name = models.CharField(max_length=100)
    author_position = models.CharField(max_length=100)
    author_image = models.ImageField(upload_to='testimonials/', blank=True, null=True)
    
    content = models.TextField()
    rating = models.PositiveIntegerField(choices=[(i, i) for i in range(1, 6)], default=5)
    
    # Display settings
    featured = models.BooleanField(default=False)
    active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-featured', 'order', '-created_at']
    
    def __str__(self):
        return f"{self.author_name} - {self.client.name}"
