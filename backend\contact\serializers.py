from rest_framework import serializers
from .models import ContactSubmission, Newsletter


class ContactSubmissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContactSubmission
        fields = [
            'name', 'email', 'phone', 'company', 'service', 'budget',
            'timeline', 'message', 'how_did_you_hear', 'website_url'
        ]
        
    def validate_email(self, value):
        """Validate email format"""
        if not value:
            raise serializers.ValidationError("Email is required.")
        return value.lower()
    
    def validate_name(self, value):
        """Validate name"""
        if not value or len(value.strip()) < 2:
            raise serializers.ValidationError("Name must be at least 2 characters long.")
        return value.strip()


class NewsletterSerializer(serializers.ModelSerializer):
    class Meta:
        model = Newsletter
        fields = ['email', 'name']
        
    def validate_email(self, value):
        """Validate email format and check for duplicates"""
        if not value:
            raise serializers.ValidationError("Email is required.")
        
        email = value.lower()
        if Newsletter.objects.filter(email=email).exists():
            raise serializers.ValidationError("This email is already subscribed to our newsletter.")
        
        return email
    
    def create(self, validated_data):
        validated_data['email'] = validated_data['email'].lower()
        return super().create(validated_data)
