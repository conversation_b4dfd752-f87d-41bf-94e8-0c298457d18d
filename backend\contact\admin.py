from django.contrib import admin
from .models import ContactSubmission, Newsletter


@admin.register(ContactSubmission)
class ContactSubmissionAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'email', 'service', 'budget', 'status', 'created_at'
    ]
    list_filter = ['service', 'budget', 'status', 'created_at']
    search_fields = ['name', 'email', 'company', 'message']
    list_editable = ['status']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Contact Information', {
            'fields': ('name', 'email', 'phone', 'company')
        }),
        ('Project Details', {
            'fields': ('service', 'budget', 'timeline', 'message', 'website_url')
        }),
        ('Additional Information', {
            'fields': ('how_did_you_hear',)
        }),
        ('Internal Tracking', {
            'fields': ('status', 'assigned_to', 'notes')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related()


@admin.register(Newsletter)
class NewsletterAdmin(admin.ModelAdmin):
    list_display = ['email', 'name', 'active', 'subscribed_at']
    list_filter = ['active', 'subscribed_at']
    search_fields = ['email', 'name']
    list_editable = ['active']
    readonly_fields = ['subscribed_at', 'updated_at']
    
    actions = ['activate_subscribers', 'deactivate_subscribers']
    
    def activate_subscribers(self, request, queryset):
        queryset.update(active=True)
        self.message_user(request, f"Activated {queryset.count()} subscribers.")
    activate_subscribers.short_description = "Activate selected subscribers"
    
    def deactivate_subscribers(self, request, queryset):
        queryset.update(active=False)
        self.message_user(request, f"Deactivated {queryset.count()} subscribers.")
    deactivate_subscribers.short_description = "Deactivate selected subscribers"
